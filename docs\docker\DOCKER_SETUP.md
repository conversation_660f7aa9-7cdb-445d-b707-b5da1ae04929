# Docker开发环境快速设置指南

## 🚀 快速开始

### 1. 配置Cloudflare认证

#### 获取API Token

1. 访问 [Cloudflare API Tokens](https://dash.cloudflare.com/profile/api-tokens)
2. 点击 "Create Token" → "Custom token"
3. **Token名称**: `shipany-stwd-docker-dev-token`
4. **设置权限**：
   - **Zone:Zone:Read**
   - **Zone:Zone Settings:Edit**
   - **Account:Workers Scripts:Edit**
   - **Account:Account Settings:Read**
5. **资源范围**：
   - **Account Resources**: All accounts（或选择特定账户）
   - **Zone Resources**: All zones（或选择特定域名）
6. 点击 "Continue to summary" → "Create Token"
7. 复制生成的Token（只显示一次，请妥善保存）

#### 获取Account ID

1. 访问 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. **从地址栏获取**（最可靠的方法）：
   - 地址栏格式：`https://dash.cloudflare.com/[ACCOUNT_ID]/...`
   - 示例：`https://dash.cloudflare.com/f2aa37b343ab10dae3dd8a6b6f98258a/home/<USER>
   - Account ID = `f2aa37b343ab10dae3dd8a6b6f98258a`

#### 配置环境变量

```bash
# 编辑 .env.docker 文件
CLOUDFLARE_API_TOKEN=your_api_token_here
CLOUDFLARE_ACCOUNT_ID=your_account_id_here
```

**示例配置**：

```bash
# 实际配置示例
CLOUDFLARE_API_TOKEN=uc1ljuROJ8Wim9xcsMAn3j6LCglTOaaxXaeQ0xA-
CLOUDFLARE_ACCOUNT_ID=f2aa37b343ab10dae3dd8a6b6f98258a
```

### 2. 启动开发环境

```bash
# 方式1: 使用便捷脚本（推荐）
./scripts/docker-dev.sh start

# 方式2: 直接使用docker-compose
docker-compose up -d
```

### 3. 进入开发容器

```bash
# 方式1: 使用便捷脚本
./scripts/docker-dev.sh shell

# 方式2: 直接使用docker-compose
docker-compose exec dev bash
```

### 4. 在容器内开发

```bash
# 启动Next.js开发服务器
pnpm dev

# 构建项目
pnpm build

# Cloudflare预览部署
pnpm cf:preview

# Cloudflare生产部署
pnpm cf:deploy

# 查看Cloudflare日志
wrangler tail
```

## 📋 常用命令

### 环境管理

```bash
./scripts/docker-dev.sh start     # 启动环境
./scripts/docker-dev.sh stop      # 停止环境
./scripts/docker-dev.sh restart   # 重启环境
./scripts/docker-dev.sh status    # 查看状态
./scripts/docker-dev.sh logs      # 查看日志
```

### 开发命令

```bash
./scripts/docker-dev.sh dev           # 启动Next.js开发服务器
./scripts/docker-dev.sh cf:preview    # Cloudflare预览部署
./scripts/docker-dev.sh cf:deploy     # Cloudflare生产部署
./scripts/docker-dev.sh wrangler tail # 查看Cloudflare日志
```

### 维护命令

```bash
./scripts/docker-dev.sh build    # 重新构建镜像
./scripts/docker-dev.sh clean    # 清理所有Docker资源
```

## 🔧 故障排查

### 常见问题

#### 1. Docker未启动

```text
❌ Docker is not running. Please start Docker Desktop.
```

**解决方案**: 启动Docker Desktop

#### 2. API Token未配置

```text
⚠️ CLOUDFLARE_API_TOKEN is not set in .env.docker
```

**解决方案**: 按照上述步骤配置API Token

#### 2.5. Account ID找不到

**问题**: 无法在右侧边栏或API Tokens页面找到Account ID

**解决方案**: 使用地址栏方法（最可靠）

- Cloudflare界面经常更新，右侧边栏可能不显示Account ID
- API Tokens页面的界面布局可能变化
- **推荐方法**: 直接从浏览器地址栏复制

  ```text
  https://dash.cloudflare.com/[这里就是Account ID]/...
  ```

#### 3. 端口冲突

```text
Error: Port 3000 is already in use
```

**解决方案**:

- 停止占用端口的进程
- 或修改docker-compose.yml中的端口映射

#### 4. 权限问题（Linux/Mac）

```text
Permission denied: ./scripts/docker-dev.sh
```

**解决方案**:

```bash
chmod +x scripts/docker-dev.sh
```

### 查看详细日志

```bash
# 查看容器日志
./scripts/docker-dev.sh logs

# 查看Docker Compose状态
docker-compose ps

# 进入容器调试
./scripts/docker-dev.sh shell
```

## 🎯 CMS重构工作流程

### 1. 启动开发环境

```bash
./scripts/docker-dev.sh start
./scripts/docker-dev.sh shell
```

### 2. 开发和测试

```bash
# 在容器内
pnpm dev                    # 启动开发服务器
# 编辑代码...
pnpm build                  # 测试构建
```

### 3. Cloudflare部署测试

```bash
# 预览部署（测试环境）
pnpm cf:preview

# 生产部署
pnpm cf:deploy

# 查看部署日志
wrangler tail
```

### 4. 停止环境

```bash
./scripts/docker-dev.sh stop
```

## 📁 文件结构

```text
├── docker-compose.yml      # Docker编排配置
├── Dockerfile.dev          # 开发环境容器
├── .env.docker            # Docker特定环境变量
├── scripts/
│   └── docker-dev.sh      # 便捷操作脚本
└── DOCKER_SETUP.md        # 本设置指南
```

## 🔒 安全注意事项

1. **不要提交API Token**: .env.docker已在.gitignore中
2. **定期轮换Token**: 建议定期更新Cloudflare API Token
3. **最小权限原则**: API Token只授予必要的权限

## 💡 提示

- 首次启动可能需要几分钟下载镜像和安装依赖
- 代码修改会实时同步到容器内
- wrangler认证信息会持久化保存
- 使用 `./scripts/docker-dev.sh help` 查看所有可用命令
