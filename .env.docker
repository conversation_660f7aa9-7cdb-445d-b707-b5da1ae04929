# =============================================================================
# Docker Development Environment Variables
# =============================================================================
# This file contains Docker-specific environment variable overrides.
# It extends the base configuration from .env.development
#
# SETUP INSTRUCTIONS:
# 1. Get your Cloudflare API Token:
#    - Visit: https://dash.cloudflare.com/profile/api-tokens
#    - Click "Create Token" -> "Custom token"
#    - Token name: shipany-stwd-docker-dev-token
#    - Permissions needed:
#      * Zone:Zone:Read
#      * Zone:Zone Settings:Edit
#      * Account:Workers Scripts:Edit
#      * Account:Account Settings:Read
#    - Account Resources: All accounts (or select specific account)
#    - Zone Resources: All zones (or select specific zones)
# 2. Get your Account ID:
#    - Visit: https://dash.cloudflare.com/
#    - Account ID is in the URL: dash.cloudflare.com/[ACCOUNT_ID]/...
#    - Example: https://dash.cloudflare.com/f2aa37b343ab10dae3dd8a6b6f98258a/home
#      Account ID = f2aa37b343ab10dae3dd8a6b6f98258a
# 3. Fill in the values below
# =============================================================================

# -----------------------------------------------------------------------------
# Cloudflare Authentication (REQUIRED)
# -----------------------------------------------------------------------------
# Replace with your actual Cloudflare API Token
CLOUDFLARE_API_TOKEN=uc1ljuROJ8Wim9xcsMAn3j6LCglTOaaxXaeQ0xA-

# Replace with your actual Cloudflare Account ID  
CLOUDFLARE_ACCOUNT_ID=f2aa37b343ab10dae3dd8a6b6f98258a

# Disable wrangler metrics collection in Docker
WRANGLER_SEND_METRICS=false

# -----------------------------------------------------------------------------
# Docker Network Configuration
# -----------------------------------------------------------------------------
# Web URL for Docker environment (accessible from host)
NEXT_PUBLIC_WEB_URL=http://localhost:3000

# Auth URL for Docker environment
AUTH_URL=http://localhost:3000/api/auth

# -----------------------------------------------------------------------------
# Development Optimizations
# -----------------------------------------------------------------------------
# Disable Next.js telemetry in Docker
NEXT_TELEMETRY_DISABLED=1

# Node.js optimizations for Docker
NODE_OPTIONS=--max-old-space-size=4096

# -----------------------------------------------------------------------------
# Optional: Database Configuration for Docker
# -----------------------------------------------------------------------------
# If using Docker Postgres, uncomment and modify:
# DATABASE_URL=********************************************/shipany

# -----------------------------------------------------------------------------
# Optional: Additional Docker-specific overrides
# -----------------------------------------------------------------------------
# Add any other environment variables that need different values in Docker
# For example, if you have external services running in Docker containers
