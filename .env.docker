# =============================================================================
# Docker Development Environment Variables
# =============================================================================
# This file contains Docker-specific environment variable overrides.
# It extends the base configuration from .env.development
#
# SETUP INSTRUCTIONS:
# 1. Get your Cloudflare API Token:
#    - Visit: https://dash.cloudflare.com/profile/api-tokens
#    - Click "Create Token" -> "Custom token"
#    - Permissions needed:
#      * Zone:Zone:Read
#      * Zone:Zone Settings:Edit  
#      * Account:Cloudflare Workers:Edit
#      * Account:Account Settings:Read
# 2. Get your Account ID:
#    - Visit: https://dash.cloudflare.com/
#    - Copy Account ID from the right sidebar
# 3. Fill in the values below
# =============================================================================

# -----------------------------------------------------------------------------
# Cloudflare Authentication (REQUIRED)
# -----------------------------------------------------------------------------
# Replace with your actual Cloudflare API Token
CLOUDFLARE_API_TOKEN=

# Replace with your actual Cloudflare Account ID  
CLOUDFLARE_ACCOUNT_ID=

# Disable wrangler metrics collection in Docker
WRANGLER_SEND_METRICS=false

# -----------------------------------------------------------------------------
# Docker Network Configuration
# -----------------------------------------------------------------------------
# Web URL for Docker environment (accessible from host)
NEXT_PUBLIC_WEB_URL=http://localhost:3000

# Auth URL for Docker environment
AUTH_URL=http://localhost:3000/api/auth

# -----------------------------------------------------------------------------
# Development Optimizations
# -----------------------------------------------------------------------------
# Disable Next.js telemetry in Docker
NEXT_TELEMETRY_DISABLED=1

# Node.js optimizations for Docker
NODE_OPTIONS=--max-old-space-size=4096

# -----------------------------------------------------------------------------
# Optional: Database Configuration for Docker
# -----------------------------------------------------------------------------
# If using Docker Postgres, uncomment and modify:
# DATABASE_URL=********************************************/shipany

# -----------------------------------------------------------------------------
# Optional: Additional Docker-specific overrides
# -----------------------------------------------------------------------------
# Add any other environment variables that need different values in Docker
# For example, if you have external services running in Docker containers
