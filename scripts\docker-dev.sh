#!/bin/bash

# =============================================================================
# Docker Development Environment Helper Script
# =============================================================================
# This script provides convenient commands for Docker-based development
# Usage: ./scripts/docker-dev.sh [command]
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker Desktop."
        exit 1
    fi
}

# Check if .env.docker exists and has required variables
check_env() {
    if [ ! -f ".env.docker" ]; then
        log_error ".env.docker file not found!"
        log_info "Please copy .env.docker.example to .env.docker and configure it."
        exit 1
    fi
    
    if ! grep -q "CLOUDFLARE_API_TOKEN=" .env.docker || [ -z "$(grep CLOUDFLARE_API_TOKEN= .env.docker | cut -d'=' -f2)" ]; then
        log_warning "CLOUDFLARE_API_TOKEN is not set in .env.docker"
        log_info "Cloudflare operations will not work until you configure the API token."
    fi
}

# Main commands
case "$1" in
    "start"|"up")
        log_info "Starting Docker development environment..."
        check_docker
        check_env
        docker-compose up -d
        log_success "Development environment started!"
        log_info "Run './scripts/docker-dev.sh shell' to enter the container"
        log_info "Or run './scripts/docker-dev.sh logs' to view logs"
        ;;
    
    "stop"|"down")
        log_info "Stopping Docker development environment..."
        docker-compose down
        log_success "Development environment stopped!"
        ;;
    
    "restart")
        log_info "Restarting Docker development environment..."
        docker-compose down
        docker-compose up -d
        log_success "Development environment restarted!"
        ;;
    
    "shell"|"bash")
        log_info "Entering development container..."
        docker-compose exec dev bash
        ;;
    
    "logs")
        log_info "Showing container logs..."
        docker-compose logs -f dev
        ;;
    
    "build")
        log_info "Rebuilding Docker images..."
        docker-compose build --no-cache
        log_success "Docker images rebuilt!"
        ;;
    
    "clean")
        log_warning "This will remove all Docker containers, images, and volumes!"
        read -p "Are you sure? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker-compose down -v
            docker system prune -af
            log_success "Docker environment cleaned!"
        else
            log_info "Clean operation cancelled."
        fi
        ;;
    
    "status")
        log_info "Docker environment status:"
        docker-compose ps
        ;;
    
    "dev")
        log_info "Starting Next.js development server in container..."
        docker-compose exec dev pnpm dev
        ;;
    
    "cf:preview")
        log_info "Running Cloudflare preview deployment..."
        docker-compose exec dev pnpm cf:preview
        ;;
    
    "cf:deploy")
        log_info "Running Cloudflare production deployment..."
        docker-compose exec dev pnpm cf:deploy
        ;;
    
    "wrangler")
        shift
        log_info "Running wrangler command: $@"
        docker-compose exec dev wrangler "$@"
        ;;
    
    "help"|"")
        echo "Docker Development Environment Helper"
        echo ""
        echo "Usage: ./scripts/docker-dev.sh [command]"
        echo ""
        echo "Commands:"
        echo "  start, up       Start the development environment"
        echo "  stop, down      Stop the development environment"
        echo "  restart         Restart the development environment"
        echo "  shell, bash     Enter the development container"
        echo "  logs            Show container logs"
        echo "  build           Rebuild Docker images"
        echo "  clean           Remove all Docker containers and images"
        echo "  status          Show container status"
        echo ""
        echo "Development Commands:"
        echo "  dev             Start Next.js development server"
        echo "  cf:preview      Cloudflare preview deployment"
        echo "  cf:deploy       Cloudflare production deployment"
        echo "  wrangler [args] Run wrangler command"
        echo ""
        echo "Examples:"
        echo "  ./scripts/docker-dev.sh start"
        echo "  ./scripts/docker-dev.sh shell"
        echo "  ./scripts/docker-dev.sh wrangler tail"
        ;;
    
    *)
        log_error "Unknown command: $1"
        log_info "Run './scripts/docker-dev.sh help' for available commands"
        exit 1
        ;;
esac
