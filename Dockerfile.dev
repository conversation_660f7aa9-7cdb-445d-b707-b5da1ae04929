# Development Dockerfile for Cloudflare Workers + Next.js
FROM node:18-alpine

# Install system dependencies for development
RUN apk add --no-cache \
    git \
    curl \
    bash \
    openssh-client \
    && yarn global add pnpm

# Set working directory
WORKDIR /app

# Setup pnpm global directory and install wrangler
RUN pnpm setup && \
    pnpm add -g wrangler@4.19.1

# Copy package files for dependency installation
COPY package.json pnpm-lock.yaml* ./

# Install dependencies
RUN pnpm install --frozen-lockfile

# Copy source configuration files
COPY contentlayer.config.ts ./
COPY next.config.mjs ./
COPY open-next.config.ts ./
COPY postcss.config.mjs ./
COPY source.config.ts ./
COPY tsconfig.json ./
COPY components.json ./

# Create necessary directories
RUN mkdir -p .next .contentlayer .open-next

# Set up git configuration (for potential git operations)
RUN git config --global user.email "<EMAIL>" && \
    git config --global user.name "Docker Dev"

# Expose ports
EXPOSE 3000 8787

# Set environment variables
ENV NODE_ENV=development
ENV PNPM_HOME="/usr/local/bin"
ENV PATH="$PNPM_HOME:$PATH"

# Default command (will be overridden by docker-compose)
CMD ["tail", "-f", "/dev/null"]
